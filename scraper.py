import time
import os
import requests
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductScraper:
    def __init__(self):
        """Khởi tạo ProductScraper với cấu hình Chrome"""
        self.setup_driver()
        self.old_website = "https://hoangminhanh.com.vn"
        self.new_website = "https://dienmayhoangminhanh.com"
        self.admin_email = "dienmayhoangminhanh.com"
        self.admin_password = "kimson@121224"
        self.images_folder = "downloaded_images"
        self.create_images_folder()

    def setup_driver(self):
        """Thiết lập Chrome driver với các options cần thiết"""
        chrome_options = Options()
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--start-maximized")

        self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        self.wait = WebDriverWait(self.driver, 10)

    def create_images_folder(self):
        """Tạo thư mục để lưu ảnh download"""
        if not os.path.exists(self.images_folder):
            os.makedirs(self.images_folder)

    def login_admin(self):
        """Đăng nhập vào trang admin của website mới"""
        try:
            logger.info("Đang đăng nhập vào trang admin...")
            self.driver.get(f"{self.new_website}/quantri/admin.asp")

            # Tìm và điền email
            email_field = self.wait.until(EC.presence_of_element_located((By.NAME, "tenmien")))
            email_field.clear()
            email_field.send_keys(self.admin_email)

            # Tìm và điền password
            password_field = self.driver.find_element(By.NAME, "matkhau")
            password_field.clear()
            password_field.send_keys(self.admin_password)

            # Nhấn nút đăng nhập
            login_button = self.driver.find_element(By.CSS_SELECTOR, "input[type='submit']")
            login_button.click()

            # Chờ đăng nhập thành công
            self.wait.until(EC.url_contains("quantri"))
            logger.info("Đăng nhập thành công!")
            return True

        except Exception as e:
            logger.error(f"Lỗi khi đăng nhập: {str(e)}")
            return False

    def check_product_exists(self, product_name, ma_nhom):
        """Kiểm tra xem sản phẩm đã tồn tại trong hệ thống chưa"""
        try:
            logger.info(f"Kiểm tra sản phẩm '{product_name}' đã tồn tại...")

            # Truy cập trang danh sách sản phẩm
            self.driver.get(f"{self.new_website}/quantri/sanpham.asp?ma_nhom={ma_nhom}")

            page_source = self.driver.page_source.lower()
            product_name_lower = product_name.lower()

            if product_name_lower in page_source:
                logger.info(f"Sản phẩm '{product_name}' đã tồn tại!")
                return True
            else:
                return False

        except Exception as e:
            logger.error(f"Lỗi khi kiểm tra sản phẩm: {str(e)}")
            return False

    def get_product_list_from_old_site(self, category_url):
        """Lấy danh sách sản phẩm từ trang danh mục của website cũ"""
        try:
            logger.info(f"Đang lấy danh sách sản phẩm từ: {category_url}")
            self.driver.get(category_url)

            product_links = self.driver.find_elements(By.CSS_SELECTOR, ".productinfo a")

            products = []
            for link in product_links:
                product_url = link.get_attribute("href")
                if product_url:
                    products.append(product_url)

            logger.info(f"Tìm thấy {len(products)} sản phẩm")
            return products

        except Exception as e:
            logger.error(f"Lỗi khi lấy danh sách sản phẩm: {str(e)}")
            return []

    def scrape_product_details(self, product_url):
        """Cào thông tin chi tiết sản phẩm từ website cũ"""
        try:
            logger.info(f"Đang cào thông tin sản phẩm: {product_url}")
            self.driver.get(product_url)

            # Lấy thông tin sản phẩm (selector cần điều chỉnh)
            product_data = {
                'name': '',
                'price': '',
                'description': '',
                'summary': '',
                'images': []
            }

            # Tên sản phẩm
            try:
                name_element = self.driver.find_element(By.CSS_SELECTOR, ".product-information h2")
                product_data['name'] = name_element.text.strip()
            except:
                product_data['name'] = "Tên sản phẩm"

            # Giá sản phẩm
            try:
                price_element = self.driver.find_element(By.CSS_SELECTOR, ".product-information span span")
                product_data['price'] = price_element.text.strip()
            except:
                product_data['price'] = "0"

            # Mô tả sản phẩm
            try:
                desc_element = self.driver.find_element(By.CSS_SELECTOR, ".single-products .productinfo")
                product_data['description'] = desc_element.get_attribute('innerHTML')
            except:
                product_data['description'] = "Mô tả sản phẩm"
            
            # Tóm tắt sản phẩm
            try:
                summary_element = self.driver.find_element(By.CSS_SELECTOR, ".product-information p:last-of-type")
                product_data['summary'] = summary_element.get_attribute('outerHTML')
            except:
                product_data['summary'] = "Tóm tắt sản phẩm"

            # Hình ảnh sản phẩm
            try:
                image_elements = self.driver.find_elements(By.CSS_SELECTOR, ".view-product img")
                for img in image_elements:
                    img_src = img.get_attribute("src")
                    if img_src:
                        product_data['images'].append(img_src)
            except:
                pass

            logger.info(f"Đã cào thông tin sản phẩm: {product_data['name']}")
            return product_data

        except Exception as e:
            logger.error(f"Lỗi khi cào thông tin sản phẩm: {str(e)}")
            return None

    def download_image(self, image_url, filename):
        """Download ảnh từ URL và lưu vào thư mục local"""
        try:
            # Tạo URL đầy đủ nếu cần
            if not image_url.startswith('http'):
                image_url = urljoin(self.old_website, image_url)

            response = requests.get(image_url, stream=True)
            response.raise_for_status()

            filepath = os.path.join(self.images_folder, filename)
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"Đã download ảnh: {filename}")
            return filepath

        except Exception as e:
            logger.error(f"Lỗi khi download ảnh {image_url}: {str(e)}")
            return None


    def upload_product_image(self, ma_so, image_path):
        """Upload ảnh sản phẩm lên website mới"""
        try:
            logger.info(f"Đang upload ảnh cho sản phẩm {ma_so}...")
            main_window = self.driver.current_window_handle
            existing_windows = set(self.driver.window_handles)

            # Click link để bật cửa sổ pop-up
            link = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//a[normalize-space(text())='Upload ảnh sản phẩm']"))
            )
            link.click()

            # Đợi cửa sổ pop-up xuất hiện
            WebDriverWait(self.driver, 10).until(
                lambda d: len(d.window_handles) > len(existing_windows)
            )

            # Lấy handle của cửa sổ pop-up
            new_window = list(set(self.driver.window_handles) - existing_windows)[0]

            # Switch sang pop-up
            self.driver.switch_to.window(new_window)
            logger.info(f"Đã switch sang cửa sổ pop-up: {self.driver.current_url}")

            # Upload file
            file_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
            )
            file_input.send_keys(os.path.abspath(image_path))

            # Bấm nút Upload
            self.driver.find_element(By.CSS_SELECTOR, "input[type='submit']").click()

            # Chờ alert và xử lý
            WebDriverWait(self.driver, 30).until(EC.alert_is_present())
            alert = self.driver.switch_to.alert
            alert.accept()

            # ✅ Đóng cửa sổ pop-up
            self.driver.close()
            self.driver.switch_to.window(main_window)
            logger.info("Upload ảnh thành công và đã tắt cửa sổ pop-up!")

            return True

        except Exception as e:
            try:
                for handle in self.driver.window_handles:
                    if handle != main_window:
                        self.driver.switch_to.window(handle)
                        self.driver.close()
                self.driver.switch_to.window(main_window)
            except Exception as inner_e:
                logger.warning(f"Không thể switch về main_window: {inner_e}")
            return False



    def create_new_product(self, product_data, ma_nhom, display_order):
        """Tạo sản phẩm mới trên website admin"""
        try:
            logger.info(f"Đang tạo sản phẩm mới: {product_data['name']}")

            # Truy cập trang thêm sản phẩm
            self.driver.get(f"{self.new_website}/quantri/add_creator_sanpham.asp?ma_congty=2832&ma_nhom={ma_nhom}")

            # Điền tên sản phẩm
            name_field = self.wait.until(EC.presence_of_element_located((By.NAME, "ten_sanpham")))
            name_field.clear()
            name_field.send_keys(product_data['name'])

            # Điền giá bán
            price_field = self.driver.find_element(By.NAME, "giaban")
            price_field.clear()
            # Làm sạch giá
            clean_price = '<b>' + product_data['price'].strip().removesuffix('đ') + '</b>'
            price_field.send_keys(clean_price)

            # Điền mô tả sản phẩm
            # Chuyển vào iframe của editor
            iframe = self.wait.until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, "iframe.cke_wysiwyg_frame"))
            )
            body = self.driver.find_element(By.TAG_NAME, "body")
            self.driver.execute_script(
                "arguments[0].innerHTML = arguments[1];",
                body,
                product_data['description']
            )
            # Switch về ngoài iframe
            self.driver.switch_to.default_content()

            logger.info("Đã điền thông tin sản phẩm, chuẩn bị upload ảnh...")
            ma_so_field = self.driver.find_element(By.CSS_SELECTOR, "input[name='ma_so']")
            ma_so = ma_so_field.get_attribute('value')
            if product_data['images']:
                image_url = product_data['images'][0]
                filename = image_url.split("/")[-1]
                image_path = self.download_image(image_url, filename)

                if image_path:
                    self.upload_product_image(ma_so, image_path)

            return True

        except Exception as e:
            logger.error(f"Lỗi khi tạo sản phẩm: {str(e)}")
            return False

    def submit_product(self):
        """Nhấn nút tạo sản phẩm để hoàn thành và trả về mã sản phẩm"""
        try:
            ma_so_field = self.driver.find_element(By.CSS_SELECTOR, "input[name='ma_so']")
            ma_so = ma_so_field.get_attribute('value')

            submit_button = self.driver.find_element(By.CSS_SELECTOR, "input[type='submit']")
            submit_button.click()


            if ma_so:
                logger.info(f"Đã tạo sản phẩm thành công với mã số: {ma_so}")
            else:
                logger.info("Đã tạo sản phẩm thất bại!")

            return ma_so
        except Exception as e:
            logger.error(f"Lỗi khi submit sản phẩm: {str(e)}")
            return None

    def update_product_summary(self, ma_so, summary):
        """Cập nhật tóm tắt sản phẩm vào trang edit"""
        try:
            logger.info(f"Đang cập nhật tóm tắt cho sản phẩm {ma_so}...")

            # Truy cập trang update sản phẩm
            update_url = f"{self.new_website}/quantri/update_sanpham.asp?ma_so={ma_so}"
            self.driver.get(update_url)

            # Tìm textarea tóm tắt sử dụng
            summary_field = self.wait.until(EC.presence_of_element_located((By.NAME, "tomtat_sudung")))
            summary_field.clear()
            summary_field.send_keys(summary)

            # Điền màu giá tiền
            color_field = self.driver.find_element(By.NAME, "giaban_lienhemua_color")
            color_field.clear()
            color_field.send_keys('#f97e6c')

            # Nhấn nút cập nhật
            update_button = self.driver.find_element(By.CSS_SELECTOR, "input[type='submit']")
            update_button.click()

            logger.info("Đã cập nhật tóm tắt sản phẩm thành công!")
            return True

        except Exception as e:
            logger.error(f"Lỗi khi cập nhật tóm tắt sản phẩm: {str(e)}")
            return False

    def process_category(self, category_url, ma_nhom):
        """Xử lý toàn bộ danh mục sản phẩm"""
        try:
            logger.info(f"Bắt đầu xử lý danh mục: {category_url}")

            # Đăng nhập admin
            if not self.login_admin():
                logger.error("Không thể đăng nhập admin!")
                return False

            # Lấy danh sách sản phẩm từ website cũ
            product_urls = self.get_product_list_from_old_site(category_url)

            if not product_urls:
                logger.warning("Không tìm thấy sản phẩm nào!")
                return False

            display_order = 1
            successful_products = 0

            for product_url in product_urls:
                try:
                    # Cào thông tin sản phẩm
                    product_data = self.scrape_product_details(product_url)
                    if not product_data:
                        continue

                    # Kiểm tra sản phẩm đã tồn tại chưa
                    if self.check_product_exists(product_data['name'], ma_nhom):
                        logger.info(f"Bỏ qua sản phẩm đã tồn tại: {product_data['name']}")
                        continue

                    # Tạo sản phẩm mới
                    if not self.create_new_product(product_data, ma_nhom, display_order):
                        continue

                    # Submit sản phẩm và lấy mã sản phẩm
                    ma_so = self.submit_product()
                    if not ma_so:
                        logger.error("Không thể tạo sản phẩm!")
                        continue

                    logger.info(f"Đã tạo thành công sản phẩm {successful_products + 1}: {product_data['name']} (Mã: {ma_so})")

                    # Cập nhật tóm tắt sản phẩm
                    if product_data['summary']:
                        self.update_product_summary(ma_so, product_data['summary'])

                    successful_products += 1
                    display_order += 1

                    # Nghỉ giữa các sản phẩm
                    time.sleep(1)

                except Exception as e:
                    logger.error(f"Lỗi khi xử lý sản phẩm {product_url}: {str(e)}")
                    continue

            logger.info(f"Hoàn thành! Đã tạo {successful_products}/{len(product_urls)} sản phẩm")
            return True

        except Exception as e:
            logger.error(f"Lỗi khi xử lý danh mục: {str(e)}")
            return False

    def close(self):
        """Đóng browser"""
        if hasattr(self, 'driver'):
            self.driver.quit()
            logger.info("Đã đóng browser")