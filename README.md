# Product Scraper - <PERSON><PERSON>o dữ liệu sản phẩm điện máy

Script này giúp cào dữ liệu sản phẩm từ website cũ (hoangminhanh.com.vn) và tự động nhập vào website mới (dienmayhoangminhanh.com).

## Cài đặt

1. Cài đặt Python dependencies:
```bash
pip install -r requirements.txt
```

2. Đảm bảo Chrome browser đã được cài đặt trên máy.

## Cách sử dụng

### 1. C<PERSON>u hình thông tin danh mục

Trong file `scraper.py`, tìm hàm `main()` và thay đổi các giá trị sau:

```python
# Cấu hình danh mục cần xử lý
category_url = "https://hoangminhanh.com.vn/products/index/dieu-hoa-daikin.html"
ma_nhom = "32889"
```

- `category_url`: URL trang danh mục sản phẩm trên website cũ
- `ma_nhom`: Mã nhóm tương ứng trên website mới

### 2. Chạy script

```bash
python scraper.py
```

## Quy trình hoạt động

1. **Đăng nhập admin**: Tự động đăng nhập vào trang admin của website mới
2. **Lấy danh sách sản phẩm**: Cào danh sách sản phẩm từ trang danh mục website cũ
3. **Kiểm tra sản phẩm đã tồn tại**: Tránh tạo trùng lặp
4. **Cào thông tin chi tiết**: Lấy tên, giá, mô tả, tóm tắt, hình ảnh từ từng sản phẩm
5. **Tạo sản phẩm mới**: Điền thông tin vào form admin và submit
6. **Cập nhật tóm tắt**: Vào trang edit để thêm tóm tắt sản phẩm
7. **Download hình ảnh**: Tải ảnh về máy local
8. **Upload hình ảnh**: Upload ảnh lên server
9. **Hoàn thành**: Sản phẩm đã được tạo đầy đủ thông tin

## Tùy chỉnh Selector

Script sử dụng các CSS selector mặc định. Bạn cần điều chỉnh các selector sau cho phù hợp với cấu trúc website thực tế:

### Website cũ (hoangminhanh.com.vn):
- `.product-item a`: Danh sách link sản phẩm
- `.product-information h2`: Tên sản phẩm
- `.product-information span span`: Giá sản phẩm
- `.single-products .productinfo`: Mô tả sản phẩm
- `.product-information p:last-of-type`: Tóm tắt sản phẩm
- `.view-product img`: Hình ảnh sản phẩm

### Website mới (dienmayhoangminhanh.com):
- `input[name="email"]`: Field email đăng nhập
- `input[name="password"]`: Field password đăng nhập
- `input[name="ten_sanpham"]`: Field tên sản phẩm
- `input[name="giaban"]`: Field giá bán
- `input[name="sanpham_order"]`: Field thứ tự hiển thị
- `iframe.cke_wysiwyg_frame`: Editor mô tả sản phẩm
- `textarea[name="tomtat_sudung"]`: Field tóm tắt sản phẩm (trang edit)
- `input[type="file"]`: Field upload file

## Lưu ý quan trọng

1. **Kiểm tra selector**: Trước khi chạy, hãy kiểm tra và điều chỉnh các CSS selector cho đúng
2. **Tốc độ**: Script có delay giữa các thao tác để tránh bị block
3. **Hình ảnh**: Ảnh được download vào thư mục `downloaded_images`
4. **Log**: Theo dõi quá trình qua log messages
5. **Backup**: Nên backup dữ liệu trước khi chạy script

## Xử lý lỗi

- Script sẽ bỏ qua sản phẩm lỗi và tiếp tục với sản phẩm tiếp theo
- Log chi tiết giúp debug các vấn đề
- Có thể dừng script bằng Ctrl+C

## Cấu trúc file

- `scraper.py`: Script chính
- `requirements.txt`: Dependencies
- `downloaded_images/`: Thư mục chứa ảnh download
- `README.md`: Hướng dẫn này

