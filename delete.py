from scraper import ProductScraper
import logging
import shutil
import os

# Thi<PERSON><PERSON> lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """
    <PERSON><PERSON> dụ cách sử dụng ProductScraper cho nhiều danh mục
    """
    
    # <PERSON>h sách các danh mục cần xử lý
    categories = [
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-panasonic.html",
            "ma_nhom": "35457"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-panasonic/page:2",
            "ma_nhom": "35457"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-mitsubishi-electric.html",
            "ma_nhom": "35458"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-toshiba.html",
            "ma_nhom": "35459"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-hitachi.html",
            "ma_nhom": "35460"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-hitachi/page:2",
            "ma_nhom": "35460"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-sanyo.html",
            "ma_nhom": "35461"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-samsung.html",
            "ma_nhom": "35462"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-lg.html",
            "ma_nhom": "35463"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-lg/page:2",
            "ma_nhom": "35463"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-electrolux.html",
            "ma_nhom": "35464"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-sharp.html",
            "ma_nhom": "35465"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-funiki.html",
            "ma_nhom": "35466"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-lanh-casper.html",
            "ma_nhom": "35467"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-dong.html",
            "ma_nhom": "35468"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-dong/page:2",
            "ma_nhom": "35468"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-dong-alaska-1.html",
            "ma_nhom": "35469"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-mat-sanaky.html",
            "ma_nhom": "35973"
        },
        {
            "old_url": "https://hoangminhanh.com.vn/products/index/tu-dong-mat-kinh-trung-bay-sanaky.html",
            "ma_nhom": "35472"
        },
    ]
    
    scraper = ProductScraper()
    
    try:
        logger.info("=== BẮT ĐẦU XỬ LÝ NHIỀU DANH MỤC ===")
        
        for i, category in enumerate(categories, 1):
            logger.info(f"\n--- Xử lý danh mục {i}/{len(categories)}---")
            logger.info(f"URL: {category['old_url']}")
            logger.info(f"Mã nhóm: {category['ma_nhom']}")
            
            # Xử lý từng danh mục
            success = scraper.process_category(category['old_url'], category['ma_nhom'])
            
            if success:
                logger.info(f"✅ Hoàn thành danh mục: {i}/{len(categories)}")
            else:
                logger.error(f"❌ Thất bại danh mục: {i}/{len(categories)}")
            
            # Nghỉ giữa các danh mục
            if i < len(categories):
                logger.info("Nghỉ 3 giây trước khi xử lý danh mục tiếp theo...")
                import time
                time.sleep(3)
        
        logger.info("\n=== HOÀN THÀNH TẤT CẢ DANH MỤC ===")
        
    except KeyboardInterrupt:
        logger.info("Người dùng dừng chương trình")
    except Exception as e:
        logger.error(f"Lỗi không mong muốn: {str(e)}")
    finally:
        scraper.close()
        # Xoá toàn bộ dữ liệu trong folder downloaded_images
        folder = "downloaded_images"
        if os.path.exists(folder):
            for filename in os.listdir(folder):
                file_path = os.path.join(folder, filename)
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)

def single_category_example():
    """
    Ví dụ xử lý một danh mục đơn lẻ
    """
    scraper = ProductScraper()
    
    try:
        # Cấu hình cho một danh mục cụ thể
        category_url = "https://hoangminhanh.com.vn/products/index/quat-chan-gio-nedfon.html"
        ma_nhom = "35456"
        
        logger.info("=== XỬ LÝ DANH MỤC ĐƠN LẺ ===")
        logger.info(f"Danh mục: {category_url}")
        logger.info(f"Mã nhóm: {ma_nhom}")
        
        success = scraper.process_category(category_url, ma_nhom)
        
        if success:
            logger.info("✅ Hoàn thành thành công!")
        else:
            logger.error("❌ Xử lý thất bại!")
            
    except Exception as e:
        logger.error(f"Lỗi: {str(e)}")
    finally:
        scraper.close()
        # Xoá toàn bộ dữ liệu trong folder downloaded_images
        folder = "downloaded_images"
        if os.path.exists(folder):
            for filename in os.listdir(folder):
                file_path = os.path.join(folder, filename)
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)

if __name__ == "__main__":
    # Cách 1: Xử lý nhiều danh mục
    main()
    
    # Cách 2: Xử lý một danh mục
    # single_category_example()


